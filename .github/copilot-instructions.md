# Virtual Power Plant Resource Manager - GitHub Copilot Instructions

## 项目概述
虚拟电厂资源管理器是一个基于Java的微服务系统，用于管理和协调虚拟电厂环境中的分布式能源资源(DERs)。该系统提供集中式资源管理、监控和控制功能。

## 项目结构
```
virtual-power-plant-resource-manager/
├── pom.xml                                    # 根POM文件
├── matrix-vpp-resource-manager-common/        # 共享工具和通用组件
│   ├── pom.xml
│   └── src/main/java/com/cet/electric/vpp/resourcemanager/common/
│       ├── bean/                              # 数据传输对象
│       ├── common/                            # 通用类和工具
│       ├── config/                            # 配置类
│       ├── constant/                          # 常量定义
│       ├── enums/                             # 枚举类
│       ├── exception/                         # 异常类
│       └── util/                              # 工具类
├── matrix-vpp-resource-manager-core/          # 核心业务逻辑
│   ├── pom.xml
│   └── src/main/java/com/cet/electric/vpp/resourcemanager/core/
│       ├── config/                            # 核心配置
│       ├── controller/                        # REST控制器
│       ├── mapper/                            # 数据访问层
│       ├── service/                           # 业务服务层
│       └── util/                              # 核心工具类
├── matrix-vpp-resource-manager-web/           # Web应用入口
│   ├── pom.xml
│   └── src/main/java/com/cet/electric/vpp/resourcemanager/web/
│       └── ResourceManagerApplication.java    # 主应用类
└── memory-bank/                               # 项目文档和上下文
```

## 技术栈
- **Java 8**: 主要编程语言
- **Spring Boot 2.x**: 应用框架
- **Maven**: 构建和依赖管理
- **MyBatis Plus**: 数据访问增强
- **Spring Security**: 安全框架
- **MySQL/PostgreSQL**: 数据库

## 编码规范

### 1. 命名约定
- **包名**: 全小写，使用点分隔，如 `com.cet.electric.vpp.resourcemanager.common`
- **类名**: 大驼峰命名法，如 `ResourceManager`, `DeviceStatusEnum`
- **方法名**: 小驼峰命名法，如 `getResourceById`, `updateResourceStatus`
- **变量名**: 小驼峰命名法，如 `resourceId`, `deviceStatus`
- **常量名**: 全大写，下划线分隔，如 `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`

### 2. 文件组织
- **Controller**: 放在 `core/controller` 包下，以 `Controller` 结尾
- **Service**: 放在 `core/service` 包下，以 `Service` 结尾
- **Mapper**: 放在 `core/mapper` 包下，以 `Mapper` 结尾
- **DTO**: 放在 `common/bean/dto` 包下，以 `DTO` 结尾
- **BO**: 放在 `common/bean/bo` 包下，以 `BO` 结尾
- **Enum**: 放在 `common/enums` 包下，以 `Enum` 结尾
- **Exception**: 放在 `common/exception` 包下，以 `Exception` 结尾

### 3. 代码模式

#### Controller层模式
```java
@RestController
@RequestMapping("/api/v1/resources")
@Api(tags = "资源管理")
public class ResourceController {
    
    @Autowired
    private ResourceService resourceService;
    
    @PostMapping
    @ApiOperation("创建资源")
    public Result<ResourceDTO> createResource(@RequestBody @Valid ResourceDTO resourceDTO) {
        ResourceDTO created = resourceService.createResource(resourceDTO);
        return Result.success(created);
    }
    
    @GetMapping("/{id}")
    @ApiOperation("获取资源详情")
    public Result<ResourceDTO> getResource(@PathVariable Long id) {
        ResourceDTO resource = resourceService.getResourceById(id);
        return Result.success(resource);
    }
}
```

#### Service层模式
```java
@Service
@Transactional
public class ResourceService {
    
    @Autowired
    private ResourceMapper resourceMapper;
    
    public ResourceDTO createResource(ResourceDTO resourceDTO) {
        // 参数验证
        validateResource(resourceDTO);
        
        // 业务逻辑处理
        Resource resource = convertToEntity(resourceDTO);
        resourceMapper.insert(resource);
        
        // 后处理
        notifyResourceCreated(resource);
        
        return convertToDTO(resource);
    }
    
    public ResourceDTO getResourceById(Long id) {
        Resource resource = resourceMapper.selectById(id);
        if (resource == null) {
            throw new BusinessException("资源不存在");
        }
        return convertToDTO(resource);
    }
}
```

#### Mapper层模式
```java
@Mapper
public interface ResourceMapper extends BaseMapper<Resource> {
    
    /**
     * 根据状态查询资源列表
     */
    List<Resource> selectByStatus(@Param("status") ResourceStatusEnum status);
    
    /**
     * 分页查询资源
     */
    IPage<Resource> selectResourcePage(IPage<Resource> page, @Param("query") ResourceQuery query);
}
```

#### 查询构建模式
```java
// 使用LambdaQueryWrapper进行类型安全查询
LambdaQueryWrapper<Resource> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(Resource::getStatus, ResourceStatusEnum.ACTIVE)
       .like(StringUtils.isNotBlank(name), Resource::getName, name)
       .ge(capacity != null, Resource::getCapacity, capacity)
       .orderByDesc(Resource::getCreateTime);

List<Resource> resources = resourceMapper.selectList(wrapper);
```

### 4. 异常处理
- 使用 `BusinessException` 处理业务异常
- 使用 `GlobalExceptionHandler` 统一异常处理
- 返回统一的 `Result` 格式

### 5. 数据传输对象
- **DTO**: 用于API接口数据传输
- **BO**: 用于业务逻辑处理
- **Entity**: 用于数据库映射

### 6. 配置管理
- 使用 `application.yml` 进行配置
- 环境特定配置使用 `application-{profile}.yml`
- 敏感配置使用环境变量或配置中心

### 7. 日志规范
- 使用 SLF4J + Logback
- 日志级别: DEBUG, INFO, WARN, ERROR
- 关键业务操作必须记录日志
- 异常必须记录完整堆栈信息

### 8. 测试规范
- 单元测试使用 JUnit 5 + Mockito
- 集成测试使用 Spring Boot Test
- 测试覆盖率要求 > 80%
- 测试类命名: `{ClassName}Test`

### 9. API文档
- 使用 Swagger/OpenAPI 3.0
- 所有API必须添加 `@ApiOperation` 注解
- 参数和返回值必须添加说明

### 10. 安全规范
- 使用 Spring Security 进行认证授权
- 敏感数据必须加密存储
- API访问必须进行权限验证
- 记录安全相关操作日志

## 依赖管理
- 使用Maven进行依赖管理
- 版本号统一在父POM中管理
- 避免依赖冲突，使用 `<dependencyManagement>`
- 定期更新依赖版本，确保安全性

## 提交规范
- 提交信息格式: `type(scope): description`
- type: feat, fix, docs, style, refactor, test, chore
- scope: 影响范围，如 controller, service, mapper
- description: 简洁描述变更内容

## 性能要求
- API响应时间 < 1秒
- 数据库查询优化，避免N+1问题
- 使用连接池管理数据库连接
- 合理使用缓存提升性能

## 部署要求
- 支持Docker容器化部署
- 使用Maven构建可执行JAR包
- 配置健康检查端点
- 支持优雅关闭
