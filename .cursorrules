# Virtual Power Plant Resource Manager - Cursor AI Rules

## 项目上下文
你正在协助开发虚拟电厂资源管理器，这是一个基于Java 8和Spring Boot 2.x的企业级微服务系统，用于管理分布式能源资源。

## 核心技术栈
- Java 8, Spring Boot 2.x, <PERSON>ven, MyBatis Plus
- MySQL/PostgreSQL, Spring Security, Swagger
- CET Electric基础设施集成

## 项目结构理解
```
virtual-power-plant-resource-manager/
├── matrix-vpp-resource-manager-common/    # 共享组件
├── matrix-vpp-resource-manager-core/      # 核心业务逻辑  
├── matrix-vpp-resource-manager-web/       # Web应用入口
└── memory-bank/                           # 项目文档
```

## 代码生成偏好

### 1. 包结构规范
- 基础包: `com.cet.electric.vpp.resourcemanager`
- 模块包: `common`, `core`, `web`
- 功能包: `controller`, `service`, `mapper`, `config`, `util`

### 2. 类命名和结构
```java
// Controller示例
@RestController
@RequestMapping("/api/v1/resources")
@Api(tags = "资源管理API")
@Slf4j
public class ResourceController {
    
    @Autowired
    private ResourceService resourceService;
    
    @PostMapping
    @ApiOperation("创建资源")
    public Result<ResourceDTO> createResource(@RequestBody @Valid ResourceDTO dto) {
        log.info("创建资源请求: {}", dto);
        ResourceDTO result = resourceService.createResource(dto);
        return Result.success(result);
    }
}

// Service示例
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ResourceService {
    
    @Autowired
    private ResourceMapper resourceMapper;
    
    public ResourceDTO createResource(ResourceDTO dto) {
        // 参数校验
        validateResourceDTO(dto);
        
        // 转换实体
        Resource resource = BeanUtil.copyProperties(dto, Resource.class);
        resource.setCreateTime(LocalDateTime.now());
        
        // 保存数据
        resourceMapper.insert(resource);
        
        log.info("资源创建成功, ID: {}", resource.getId());
        return BeanUtil.copyProperties(resource, ResourceDTO.class);
    }
}

// Mapper示例
@Mapper
public interface ResourceMapper extends BaseMapper<Resource> {
    
    /**
     * 根据状态查询资源列表
     */
    @Select("SELECT * FROM vpp_resource WHERE status = #{status}")
    List<Resource> selectByStatus(@Param("status") String status);
    
    /**
     * 分页查询资源
     */
    IPage<Resource> selectResourcePage(IPage<Resource> page, 
                                     @Param("ew") Wrapper<Resource> wrapper);
}
```

### 3. 数据库操作模式
```java
// 使用LambdaQueryWrapper
LambdaQueryWrapper<Resource> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(Resource::getStatus, ResourceStatusEnum.ACTIVE)
       .like(StringUtils.isNotBlank(name), Resource::getName, name)
       .orderByDesc(Resource::getCreateTime);

// 分页查询
Page<Resource> page = new Page<>(pageNum, pageSize);
IPage<Resource> result = resourceMapper.selectPage(page, wrapper);

// 批量操作
resourceMapper.insertBatch(resourceList);
```

### 4. 异常处理模式
```java
// 业务异常
if (resource == null) {
    throw new BusinessException("资源不存在");
}

// 全局异常处理器
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.error("业务异常: {}", e.getMessage());
        return Result.error(e.getMessage());
    }
}
```

### 5. 配置类模式
```java
@Configuration
@EnableConfigurationProperties
@Slf4j
public class ResourceManagerConfig {
    
    @Bean
    @ConditionalOnMissingBean
    public ResourceProcessor resourceProcessor() {
        return new DefaultResourceProcessor();
    }
}
```

## 响应格式偏好

### 1. 统一返回格式
```java
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    
    public static <T> Result<T> success(T data) {
        return new Result<>(200, "操作成功", data);
    }
    
    public static <T> Result<T> error(String message) {
        return new Result<>(500, message, null);
    }
}
```

### 2. 分页返回格式
```java
public class PageResult<T> {
    private Long total;
    private List<T> records;
    private Long current;
    private Long size;
}
```

## 代码质量要求

### 1. 必须包含的注解
- `@Slf4j` 用于日志
- `@Api` 和 `@ApiOperation` 用于API文档
- `@Valid` 用于参数校验
- `@Transactional` 用于事务管理

### 2. 异常处理
- 所有Service方法必须处理异常
- 使用BusinessException处理业务异常
- 记录详细的错误日志

### 3. 日志规范
```java
// 方法入口日志
log.info("开始执行资源创建, 参数: {}", dto);

// 关键步骤日志
log.debug("资源校验通过, 开始保存数据");

// 异常日志
log.error("资源创建失败, 原因: {}", e.getMessage(), e);

// 方法出口日志
log.info("资源创建完成, 结果: {}", result);
```

### 4. 参数校验
```java
// DTO校验注解
public class ResourceDTO {
    @NotBlank(message = "资源名称不能为空")
    private String name;
    
    @NotNull(message = "资源类型不能为空")
    private ResourceTypeEnum type;
    
    @DecimalMin(value = "0", message = "容量不能小于0")
    private BigDecimal capacity;
}
```

## 特定业务规则

### 1. 资源管理
- 资源状态使用枚举: ACTIVE, INACTIVE, MAINTENANCE
- 资源类型: SOLAR, WIND, BATTERY, DEMAND_RESPONSE
- 所有资源操作必须记录审计日志

### 2. 设备管理
- 设备状态: ONLINE, OFFLINE, ERROR
- 设备类型与资源类型关联
- 设备状态变更需要发送通知

### 3. 数据库设计
- 表名前缀: `vpp_`
- 主键字段: `id` (Long类型)
- 审计字段: `create_time`, `update_time`, `create_by`, `update_by`
- 逻辑删除字段: `deleted` (0-未删除, 1-已删除)

## 错误处理偏好

### 1. 参数校验错误
```java
@ExceptionHandler(MethodArgumentNotValidException.class)
public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
    String message = e.getBindingResult().getFieldErrors().stream()
        .map(FieldError::getDefaultMessage)
        .collect(Collectors.joining(", "));
    return Result.error("参数校验失败: " + message);
}
```

### 2. 数据库操作错误
```java
try {
    resourceMapper.insert(resource);
} catch (DuplicateKeyException e) {
    throw new BusinessException("资源名称已存在");
} catch (DataAccessException e) {
    log.error("数据库操作失败", e);
    throw new BusinessException("数据保存失败");
}
```

## 性能优化偏好

### 1. 查询优化
- 使用索引字段进行查询
- 避免SELECT *，明确指定字段
- 大数据量查询使用分页
- 复杂查询考虑使用缓存

### 2. 事务管理
- 只读操作使用 `@Transactional(readOnly = true)`
- 明确指定回滚异常 `rollbackFor = Exception.class`
- 避免长事务，及时提交

## 安全考虑

### 1. 权限控制
```java
@PreAuthorize("hasRole('ADMIN') or hasPermission(#id, 'RESOURCE', 'READ')")
public ResourceDTO getResource(Long id) {
    // 方法实现
}
```

### 2. 数据脱敏
- 敏感数据不记录到日志
- API返回时脱敏处理
- 数据库存储加密

## 测试代码生成

### 1. 单元测试模板
```java
@ExtendWith(MockitoExtension.class)
class ResourceServiceTest {
    
    @Mock
    private ResourceMapper resourceMapper;
    
    @InjectMocks
    private ResourceService resourceService;
    
    @Test
    void testCreateResource() {
        // Given
        ResourceDTO dto = new ResourceDTO();
        dto.setName("测试资源");
        
        // When
        when(resourceMapper.insert(any())).thenReturn(1);
        ResourceDTO result = resourceService.createResource(dto);
        
        // Then
        assertThat(result.getName()).isEqualTo("测试资源");
        verify(resourceMapper).insert(any());
    }
}
```

记住：始终遵循企业级Java开发最佳实践，确保代码的可维护性、可扩展性和安全性。
